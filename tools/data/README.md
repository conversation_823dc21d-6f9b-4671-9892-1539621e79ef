# Data Preparation for MMFewShot

It is recommended to symlink the dataset root to `$MMFEWSHOT/data`.
If your folder structure is different, you may need to change the corresponding paths in config files.

# Few Shot Classification

Datasets supported in MMFewShot:

- [CUB Dataset](classification/cub/README.md) \[ [Homepage](http://www.vision.caltech.edu/visipedia/CUB-200-2011.html) \]
- [Mini ImageNet Dataset](classification/mini-imagenet/README.md) \[ [Homepage](https://image-net.org/challenges/LSVRC/2012/) \] \[ [Split](https://github.com/twitter/meta-learning-lstm/tree/master/data/miniImagenet) \]
- [Tiered ImageNet Dataset](classification/tiered-imagenet/README.md) \[ [Homepage](https://image-net.org/challenges/LSVRC/2012/) \] \[ [Split](https://github.com/renmengye/few-shot-ssl-public#tieredimagenet) \]

# Few Shot Detection

Datasets supported in MMFewShot:

- [COCO Dataset](detection/coco/README.md) \[ [Homepage](https://cocodataset.org/#home) \] \[ [Split](http://dl.yf.io/fs-det/datasets/cocosplit/) \]
- [VOC Dataset](detection/voc/README.md) \[ [Homepage](http://host.robots.ox.ac.uk/pascal/VOC/) \] \[ [Split](http://dl.yf.io/fs-det/datasets/vocsplit/) \]
