# Data Preparation for Few Shot Classification

It is recommended to symlink the dataset root to `$MMFEWSHOT/data`.
If your folder structure is different, you may need to change the corresponding paths in config files.

Datasets supported in MMFewShot:

- [CUB Dataset](cub/README.md) \[ [Homepage](http://www.vision.caltech.edu/visipedia/CUB-200-2011.html) \]
- [Mini ImageNet Dataset](mini-imagenet/README.md) \[ [Homepage](https://image-net.org/challenges/LSVRC/2012/) \] \[ [Split](https://github.com/twitter/meta-learning-lstm/tree/master/data/miniImagenet) \]
- [Tiered ImageNet Dataset](tiered-imagenet/README.md) \[ [Homepage](https://image-net.org/challenges/LSVRC/2012/) \] \[ [Split](https://github.com/renmengye/few-shot-ssl-public#tieredimagenet) \]
